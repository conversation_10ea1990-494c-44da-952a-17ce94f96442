import { Card, Content, Form, Table } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';


export const Component = function() {


    return <Content>
        <Row className='g-3'>
            <Col md={3}>
                <Card>
                    <Form
                        schema={{
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    title: '名称'
                                },
                                keyword: {
                                    type: 'string',
                                    title: '关键词'
                                }
                            }
                        }}
                        uiSchema={{
                            query: {
                                'ui:widget': 'textarea',
                                'ui:options': {
                                    rows: 5,
                                    placeholder: '请输入查询内容'
                                }
                            },
                        }}
                        submitText={'测试'}
                    />
                </Card>
            </Col>
            <Col md={9}>
                <Table source={``} columns={[]} />
            </Col>
        </Row>
    </Content>;
};
