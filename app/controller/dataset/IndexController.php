<?php

namespace app\controller\dataset;

use app\BaseController;
use app\lib\Hashids;
use app\model\Dataset;
use app\model\Setting;
use think\ai\Client;
use think\exception\ValidateException;
use think\helper\Arr;

class IndexController extends BaseController
{
    use WithDataset;

    protected $routeName = 'id';

    public function index()
    {
        $id = $this->request->param('id');

        $query = $this->space->datasets()->order('id desc');

        if (!empty($id)) {
            return $query->whereIn('id', array_map(function ($id) {
                return Hashids::decode($id);
            }, $id))->select();
        } else {
            return $query->paginate();
        }
    }

    public function read()
    {
        return $this->dataset;
    }

    public function save(Client $client)
    {
        $this->space->checkQuota('dataset');

        $params = $this->validate([
            'name|名称'        => 'require',
            'description|描述' => '',
            'type|类型'        => 'in:web',
            'sitemap|站点地图' => function ($value, $data) {
                if (!empty($data['type']) && $data['type'] == 'web' && empty($value)) {
                    return '站点地图不能为空';
                }
                return true;
            },
            'freq|更新频率'    => 'integer|min:0',
        ]);

        $models = $client->model()->list(['type' => 'text']);
        $code   = Setting::read('model.embedding');
        $model  = Arr::first($models, function ($model) use ($code) {
            return $model['code'] == $code;
        });

        if (empty($model)) {
            throw new ValidateException('向量模型不存在');
        }

        $data = [
            'name'        => $params['name'],
            'description' => $params['description'] ?? '',
            'model'       => $code,
            'length'      => Arr::get($model, 'params.size', 1536),
            'user_id'     => $this->user->id,
        ];

        switch ($params['type'] ?? 'common') {
            case 'web':
                $data['type']   = Dataset::TYPE_WEB;
                $data['config'] = [
                    'sitemap' => $params['sitemap'],
                ];
                $data['freq']   = $params['freq'] ?? 0;
                $data['status'] = 0;
                break;
            default:
                $data['type'] = Dataset::TYPE_COMMON;
        }

        return $this->space->datasets()->save($data);
    }

    public function update()
    {
        $params = $this->validate([
            'name|名称'        => 'require',
            'description|描述' => '',
            'sitemap|站点地图' => function ($value) {
                if ($this->dataset->isWeb() && empty($value)) {
                    return '站点地图不能为空';
                }
                return true;
            },
            'freq|更新频率'    => 'integer|min:0',
        ]);

        $data = [
            'name'        => $params['name'],
            'description' => $params['description'] ?? '',
        ];

        switch ($this->dataset->type) {
            case Dataset::TYPE_WEB:
                $data['config'] = [
                    'sitemap' => $params['sitemap'],
                ];
                $data['freq']   = $params['freq'] ?? 0;
                break;
        }

        return $this->dataset->save($data);
    }

    public function sync()
    {
        if ($this->dataset->status != 1 && $this->dataset->status != -1) {
            abort(404);
        }
        $this->dataset->sync();
    }

    public function delete()
    {
        $this->dataset->delete();
    }

    public function recall()
    {
        $params = $this->validate([
            'query|查询内容' => 'require',
        ]);

        $query = $params['query'];

        $results = $this->dataset->search($query, 9);

        return json($results);
    }
}
